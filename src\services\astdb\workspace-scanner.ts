/**
 * Workspace scanner for building AST database from project files
 * Integrates with tree-sitter parsing service to analyze code structure
 */

import * as path from "path"
import * as fs from "fs/promises"
import { listFiles } from "@services/glob/list-files"
import { parseSourceCodeForDefinitionsTopLevel } from "@services/tree-sitter"
import { loadRequiredLanguageParsers, LanguageParser } from "@services/tree-sitter/languageParser"
import { ClineIgnoreController } from "@core/ignore/ClineIgnoreController"
import { AstDB } from "./ast-db"
import { AstDefinition, AstUsage, SymbolType, AstStatus } from "./ast-structs"
import { AstLogger, createLogger, withErrorHandling } from "./logger"

export interface ScanProgress {
	totalFiles: number
	processedFiles: number
	currentFile: string
	errors: string[]
	status: "scanning" | "parsing" | "indexing" | "complete" | "error"
	startTime: Date
	elapsedMs: number
	estimatedRemainingMs: number
	filesPerSecond: number
	definitionsFound: number
	usagesFound: number
	bytesProcessed: number
}

export interface ScanOptions {
	maxFiles?: number
	includeExtensions?: string[]
	excludePatterns?: string[]
	onProgress?: (progress: ScanProgress) => void
}

export class WorkspaceScanner {
	private astDb: AstDB
	private clineIgnoreController?: ClineIgnoreController
	private logger: AstLogger

	constructor(astDb: AstDB, clineIgnoreController?: ClineIgnoreController) {
		this.astDb = astDb
		this.clineIgnoreController = clineIgnoreController
		this.logger = createLogger("WorkspaceScanner")
	}

	/**
	 * Scan the workspace and build AST database
	 */
	async scanWorkspace(workspacePath: string, options: ScanOptions = {}): Promise<void> {
		const {
			maxFiles = 1000,
			includeExtensions = [
				"js",
				"jsx",
				"ts",
				"tsx",
				"py",
				"rs",
				"go",
				"c",
				"h",
				"cpp",
				"hpp",
				"cs",
				"rb",
				"java",
				"php",
				"swift",
				"kt",
			],
			excludePatterns = ["node_modules", ".git", "dist", "build", ".vscode"],
			onProgress,
		} = options

		const startTime = new Date()
		const progress: ScanProgress = {
			totalFiles: 0,
			processedFiles: 0,
			currentFile: "",
			errors: [],
			status: "scanning",
			startTime,
			elapsedMs: 0,
			estimatedRemainingMs: 0,
			filesPerSecond: 0,
			definitionsFound: 0,
			usagesFound: 0,
			bytesProcessed: 0,
		}

		try {
			this.logger.info("Starting workspace scan", {
				workspacePath,
				maxFiles,
				includeExtensions: includeExtensions.length,
			})

			// Update AST DB status
			this.astDb.updateStatus({ astate: "indexing", filesUnparsed: 0, filesTotal: 0 })

			// Get all files in workspace
			onProgress?.(progress)
			this.logger.info("Scanning for files", { workspacePath, maxFiles })
			const [allFiles] = await listFiles(workspacePath, false, maxFiles * 2)
			this.logger.info("Found files", { count: allFiles.length })

			// Filter files by extension
			this.logger.info("Filtering files by extension", { includeExtensions })
			const codeFiles = allFiles
				.filter((file) => {
					const ext = path.extname(file).toLowerCase().slice(1)
					return includeExtensions.includes(ext)
				})
				.filter((file) => {
					// Exclude patterns
					const relativePath = path.relative(workspacePath, file)
					return !excludePatterns.some((pattern) => relativePath.includes(pattern))
				})
				.slice(0, maxFiles)

			this.logger.info("Filtered code files", { count: codeFiles.length })

			// Filter files for access if controller is provided
			const allowedFiles = this.clineIgnoreController ? this.clineIgnoreController.filterPaths(codeFiles) : codeFiles
			this.logger.info("Final allowed files", { count: allowedFiles.length })

			progress.totalFiles = allowedFiles.length
			progress.status = "parsing"
			this.astDb.updateStatus({
				astate: "indexing",
				filesTotal: progress.totalFiles,
				filesUnparsed: progress.totalFiles,
			})
			onProgress?.(progress)

			// Load required language parsers
			this.logger.info("Loading language parsers")
			const languageParsers = await loadRequiredLanguageParsers(allowedFiles)
			this.logger.info("Language parsers loaded", { parsers: Object.keys(languageParsers) })

			// Process files
			for (let i = 0; i < allowedFiles.length; i++) {
				const filePath = allowedFiles[i]
				progress.currentFile = path.relative(workspacePath, filePath)
				progress.processedFiles = i

				// Update timing and performance metrics
				const now = new Date()
				progress.elapsedMs = now.getTime() - startTime.getTime()
				progress.filesPerSecond = progress.processedFiles / (progress.elapsedMs / 1000)

				if (progress.filesPerSecond > 0) {
					const remainingFiles = progress.totalFiles - progress.processedFiles
					progress.estimatedRemainingMs = (remainingFiles / progress.filesPerSecond) * 1000
				}

				onProgress?.(progress)

				try {
					const fileStats = await fs.stat(filePath)
					progress.bytesProcessed += fileStats.size

					const definitions = await this.processFile(filePath, languageParsers, workspacePath)
					progress.definitionsFound += definitions.length
					progress.usagesFound += definitions.reduce((sum, def) => sum + def.usages.length, 0)
				} catch (error) {
					const errorMsg = `Error processing ${progress.currentFile}: ${error}`
					progress.errors.push(errorMsg)
					this.astDb.addError(filePath, 0, errorMsg)
				}

				// Update status
				this.astDb.updateStatus({
					filesUnparsed: progress.totalFiles - progress.processedFiles - 1,
					astIndexSymbolsTotal: progress.definitionsFound,
					astIndexUsagesTotal: progress.usagesFound,
				})
			}

			progress.processedFiles = allowedFiles.length
			progress.status = "complete"
			progress.currentFile = ""

			// Update final status with complete information
			this.astDb.updateStatus({
				astate: "ready",
				filesUnparsed: 0,
				filesTotal: allowedFiles.length,
				astIndexFilesTotal: allowedFiles.length,
				astMaxFilesHit: allowedFiles.length >= maxFiles,
			})

			// Force a final status update to ensure all counters are accurate
			this.astDb.updateStatus({})

			onProgress?.(progress)

			this.logger.info("Workspace scan completed successfully", {
				totalFiles: allowedFiles.length,
				definitionsFound: progress.definitionsFound,
				usagesFound: progress.usagesFound,
				bytesProcessed: progress.bytesProcessed,
			})
		} catch (error) {
			progress.status = "error"
			progress.errors.push(`Workspace scan failed: ${error}`)
			this.astDb.updateStatus({ astate: "error" })
			this.astDb.addError(workspacePath, 0, `Workspace scan failed: ${error}`)
			onProgress?.(progress)
			throw error
		}
	}

	/**
	 * Process a single file and extract AST definitions
	 */
	private async processFile(
		filePath: string,
		languageParsers: LanguageParser,
		workspacePath: string,
	): Promise<AstDefinition[]> {
		if (this.clineIgnoreController && !this.clineIgnoreController.validateAccess(filePath)) {
			return []
		}

		try {
			const fileContent = await fs.readFile(filePath, "utf8")
			const ext = path.extname(filePath).toLowerCase().slice(1)
			const { parser, query } = languageParsers[ext] || {}

			if (!parser || !query) {
				return [] // Skip unsupported file types
			}

			// Parse the file content into AST
			const tree = parser.parse(fileContent)
			const captures = query.captures(tree.rootNode)

			// Sort captures by position
			captures.sort((a, b) => a.node.startPosition.row - b.node.startPosition.row)

			// Extract definitions from captures
			const definitions = this.extractDefinitionsFromCaptures(captures, fileContent, filePath, workspacePath)

			// Store definitions in database
			for (const definition of definitions) {
				await this.astDb.storeDefinition(definition)
			}

			return definitions
		} catch (error) {
			throw new Error(`Failed to process file ${filePath}: ${error}`)
		}
	}

	/**
	 * Extract AST definitions from tree-sitter captures
	 */
	private extractDefinitionsFromCaptures(
		captures: any[],
		fileContent: string,
		filePath: string,
		workspacePath: string,
	): AstDefinition[] {
		const definitions: AstDefinition[] = []
		const lines = fileContent.split("\n")
		const relativePath = path.relative(workspacePath, filePath)

		// Process captures to find definitions
		const processedDefinitions = new Set<string>()

		for (const capture of captures) {
			const { node, name } = capture

			// Only process name captures that indicate definitions
			if (name.includes("name") && (name.includes("definition") || name.startsWith("name"))) {
				const startLine = node.startPosition.row
				const symbolName = node.text
				const definitionKey = `${startLine}-${symbolName}`

				// Avoid duplicate definitions
				if (processedDefinitions.has(definitionKey)) {
					continue
				}
				processedDefinitions.add(definitionKey)

				try {
					const definition = this.createDefinitionFromCapture(capture, lines, relativePath, filePath)
					if (definition) {
						definitions.push(definition)
					}
				} catch (error) {
					// Skip malformed definitions
					console.warn(`Failed to create definition at line ${startLine + 1} in ${relativePath}:`, error)
				}
			}
		}

		return definitions
	}

	/**
	 * Create an AST definition from a tree-sitter capture
	 */
	private createDefinitionFromCapture(
		capture: any,
		lines: string[],
		relativePath: string,
		filePath: string,
	): AstDefinition | null {
		const { node, name } = capture
		const startLine = node.startPosition.row
		const endLine = node.endPosition.row

		// Extract symbol name
		const symbolName = node.text?.trim()
		if (!symbolName) return null

		// Determine symbol type from capture name
		let symbolType = SymbolType.Unknown
		if (name.includes("function")) symbolType = SymbolType.Function
		else if (name.includes("class")) symbolType = SymbolType.Class
		else if (name.includes("method")) symbolType = SymbolType.Method
		else if (name.includes("variable")) symbolType = SymbolType.Variable
		else if (name.includes("property")) symbolType = SymbolType.Property
		else if (name.includes("interface")) symbolType = SymbolType.Interface
		else if (name.includes("enum")) symbolType = SymbolType.Enum
		else if (name.includes("type")) symbolType = SymbolType.Type
		else if (name.includes("module")) symbolType = SymbolType.Module

		// Create official path - include file and symbol name
		const officialPath = [relativePath, symbolName]

		// Try to find the parent node for better context
		let parentNode = node.parent
		let contextPath = [symbolName]

		// Walk up the tree to find containing classes/modules
		while (parentNode) {
			if (
				parentNode.type === "class_declaration" ||
				parentNode.type === "class" ||
				parentNode.type === "module" ||
				parentNode.type === "namespace"
			) {
				// Find the name node of the parent
				const nameNode = parentNode.children?.find(
					(child: any) =>
						child.type === "identifier" || child.type === "type_identifier" || child.type === "property_identifier",
				)

				if (nameNode && nameNode.text) {
					contextPath.unshift(nameNode.text)
				}
			}
			parentNode = parentNode.parent
		}

		// Update official path with context
		if (contextPath.length > 1) {
			officialPath.splice(1, 0, ...contextPath.slice(0, -1))
		}

		// Create basic usage (self-reference)
		const usages: AstUsage[] = [
			{
				targetsForGuesswork: [symbolName],
				resolvedAs: officialPath.join("::"),
				debugHint: "definition",
				uline: startLine + 1, // Convert to 1-based
			},
		]

		return {
			officialPath,
			symbolType,
			usages,
			resolvedType: symbolType,
			thisIsAClass: symbolType === SymbolType.Class ? symbolName : "",
			thisClassDerivedFrom: [],
			cpath: filePath,
			declLine1: startLine + 1, // Convert to 1-based
			declLine2: endLine + 1, // Convert to 1-based
			bodyLine1: startLine + 1,
			bodyLine2: endLine + 1,
		}
	}

	/**
	 * Get current scan status
	 */
	getStatus(): AstStatus {
		return this.astDb.getStatus()
	}

	/**
	 * Clear workspace data and rescan
	 */
	async rescanWorkspace(workspacePath: string, options: ScanOptions = {}): Promise<void> {
		// Clear existing data for this workspace
		const status = this.astDb.getStatus()
		this.astDb.updateStatus({ ...status, astate: "clearing" })

		// Note: In a real implementation, we might want to clear only files from this workspace
		// For now, we'll just proceed with the scan which will update existing entries

		await this.scanWorkspace(workspacePath, options)
	}
}

/**
 * Convenience function to create and use a workspace scanner
 */
export async function scanWorkspaceForAST(
	workspacePath: string,
	options: ScanOptions & { dbPath?: string } = {},
): Promise<{ astDb: AstDB; scanner: WorkspaceScanner }> {
	const { dbPath, ...scanOptions } = options

	// Create AST database
	const astDb = new AstDB(dbPath)

	// Create scanner
	const scanner = new WorkspaceScanner(astDb)

	// Scan workspace
	await scanner.scanWorkspace(workspacePath, scanOptions)

	return { astDb, scanner }
}

/**
 * Get workspace scan progress as a readable string
 */
export function formatScanProgress(progress: ScanProgress): string {
	const {
		totalFiles,
		processedFiles,
		currentFile,
		status,
		errors,
		elapsedMs,
		estimatedRemainingMs,
		filesPerSecond,
		definitionsFound,
		usagesFound,
		bytesProcessed,
	} = progress

	let message = `Status: ${status}`

	if (totalFiles > 0) {
		const percentage = Math.round((processedFiles / totalFiles) * 100)
		message += ` | Progress: ${processedFiles}/${totalFiles} (${percentage}%)`
	}

	if (currentFile) {
		message += ` | Current: ${currentFile}`
	}

	// Performance metrics
	if (elapsedMs > 0) {
		const elapsedSec = Math.round(elapsedMs / 1000)
		message += ` | Elapsed: ${elapsedSec}s`

		if (filesPerSecond > 0) {
			message += ` | Speed: ${filesPerSecond.toFixed(1)} files/s`
		}

		if (estimatedRemainingMs > 0) {
			const remainingSec = Math.round(estimatedRemainingMs / 1000)
			message += ` | ETA: ${remainingSec}s`
		}
	}

	// Results
	if (definitionsFound > 0) {
		message += ` | Definitions: ${definitionsFound}`
	}

	if (usagesFound > 0) {
		message += ` | Usages: ${usagesFound}`
	}

	if (bytesProcessed > 0) {
		const mbProcessed = (bytesProcessed / (1024 * 1024)).toFixed(1)
		message += ` | Processed: ${mbProcessed}MB`
	}

	if (errors.length > 0) {
		message += ` | Errors: ${errors.length}`
	}

	return message
}

/**
 * Create a detailed progress report
 */
export function createProgressReport(progress: ScanProgress): {
	summary: string
	details: {
		performance: string
		results: string
		errors: string[]
	}
} {
	const summary = formatScanProgress(progress)

	const performance =
		`Processing ${progress.filesPerSecond.toFixed(1)} files/second, ` +
		`${(progress.bytesProcessed / (1024 * 1024)).toFixed(1)}MB processed in ` +
		`${Math.round(progress.elapsedMs / 1000)}s`

	const results =
		`Found ${progress.definitionsFound} definitions and ` +
		`${progress.usagesFound} usages across ${progress.processedFiles} files`

	return {
		summary,
		details: {
			performance,
			results,
			errors: progress.errors,
		},
	}
}
