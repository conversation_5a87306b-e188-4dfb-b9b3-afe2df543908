#!/usr/bin/env node

/**
 * AST Database Validation Script
 * Validates that all AST database functionality is working correctly
 */

const fs = require('fs').promises
const path = require('path')

class AstDatabaseValidator {
	constructor() {
		this.results = []
		this.rootDir = path.join(__dirname, '..')
	}

	async validate() {
		console.log('🔍 Validating AST Database Implementation...\n')

		await this.validateFileStructure()
		await this.validateProtobufGeneration()
		await this.validateTypeDefinitions()
		await this.validateFrontendIntegration()
		await this.validateConfigurationManagement()
		
		this.generateReport()
	}

	async validateFileStructure() {
		console.log('📁 Validating file structure...')

		const requiredFiles = [
			// Backend files
			'src/services/astdb-service/AstDatabaseService.ts',
			'src/services/astdb/workspace-scanner.ts',
			'src/services/astdb/ast-db.ts',
			'src/core/controller/astDatabase/GetScanProgress.ts',
			
			// Frontend files
			'webview-ui/src/components/settings/CodebaseSettingsSection.tsx',
			'webview-ui/src/components/settings/FeatureSettingsSection.tsx',
			
			// Proto files
			'src/shared/proto/astdb.ts',
			'proto/astdb.proto',
			
			// Test files
			'test/ast-database-integration.test.ts',
			'webview-ui/src/test/CodebaseSettingsSection.test.tsx'
		]

		for (const file of requiredFiles) {
			await this.checkFile(file)
		}
	}

	async validateProtobufGeneration() {
		console.log('🔧 Validating protobuf generation...')

		// Check that GetScanProgress method exists in proto
		const protoContent = await this.readFile('proto/astdb.proto')
		if (protoContent.includes('GetScanProgress')) {
			this.addResult('✅ GetScanProgress method in proto', true)
		} else {
			this.addResult('❌ GetScanProgress method missing in proto', false)
		}

		// Check generated TypeScript proto file
		const tsProtoContent = await this.readFile('src/shared/proto/astdb.ts')
		if (tsProtoContent.includes('getScanProgress')) {
			this.addResult('✅ getScanProgress in generated TS proto', true)
		} else {
			this.addResult('❌ getScanProgress missing in generated TS proto', false)
		}

		// Check controller exists
		await this.checkFile('src/core/controller/astDatabase/GetScanProgress.ts')
	}

	async validateTypeDefinitions() {
		console.log('🔤 Validating type definitions...')

		// Check AstDatabaseService has required methods
		const serviceContent = await this.readFile('src/services/astdb-service/AstDatabaseService.ts')
		
		const requiredMethods = [
			'getScanProgress',
			'getCurrentScanProgress',
			'startWorkspaceScan',
			'getDatabaseStatus'
		]

		for (const method of requiredMethods) {
			if (serviceContent.includes(method)) {
				this.addResult(`✅ AstDatabaseService.${method}`, true)
			} else {
				this.addResult(`❌ AstDatabaseService.${method} missing`, false)
			}
		}

		// Check ExtensionMessage has AST database fields
		const extensionMessageContent = await this.readFile('src/shared/ExtensionMessage.ts')
		const astFields = [
			'astDatabaseAutoScan',
			'astDatabaseMaxFiles',
			'astDatabaseIncludeExtensions',
			'astDatabaseExcludePatterns'
		]

		for (const field of astFields) {
			if (extensionMessageContent.includes(field)) {
				this.addResult(`✅ ExtensionMessage.${field}`, true)
			} else {
				this.addResult(`❌ ExtensionMessage.${field} missing`, false)
			}
		}
	}

	async validateFrontendIntegration() {
		console.log('🖥️ Validating frontend integration...')

		// Check CodebaseSettingsSection has real progress monitoring
		const codebaseContent = await this.readFile('webview-ui/src/components/settings/CodebaseSettingsSection.tsx')
		
		if (codebaseContent.includes('startProgressMonitoring')) {
			this.addResult('✅ Real progress monitoring implemented', true)
		} else {
			this.addResult('❌ Real progress monitoring missing', false)
		}

		if (codebaseContent.includes('getScanProgress')) {
			this.addResult('✅ getScanProgress call in frontend', true)
		} else {
			this.addResult('❌ getScanProgress call missing in frontend', false)
		}

		if (!codebaseContent.includes('mockScanProcess')) {
			this.addResult('✅ Mock scan process removed', true)
		} else {
			this.addResult('❌ Mock scan process still present', false)
		}

		// Check FeatureSettingsSection has AST database config
		const featureContent = await this.readFile('webview-ui/src/components/settings/FeatureSettingsSection.tsx')
		
		if (featureContent.includes('astDatabaseAutoScan')) {
			this.addResult('✅ AST database config in Features', true)
		} else {
			this.addResult('❌ AST database config missing in Features', false)
		}
	}

	async validateConfigurationManagement() {
		console.log('⚙️ Validating configuration management...')

		// Check that package.json doesn't have global AST database configs
		const packageContent = await this.readFile('package.json')
		
		if (!packageContent.includes('cline.astDatabase.autoScan')) {
			this.addResult('✅ Global AST database config removed from package.json', true)
		} else {
			this.addResult('❌ Global AST database config still in package.json', false)
		}

		// Check state management
		const stateContent = await this.readFile('src/core/storage/state.ts')
		
		if (stateContent.includes('astDatabaseAutoScan')) {
			this.addResult('✅ AST database state management', true)
		} else {
			this.addResult('❌ AST database state management missing', false)
		}

		// Check extension activation
		const extensionContent = await this.readFile('src/extension.ts')
		
		if (extensionContent.includes('initializeAstDatabaseAutoScan')) {
			this.addResult('✅ Auto-scan initialization in extension', true)
		} else {
			this.addResult('❌ Auto-scan initialization missing', false)
		}
	}

	async checkFile(filePath) {
		try {
			await fs.access(path.join(this.rootDir, filePath))
			this.addResult(`✅ ${filePath}`, true)
		} catch (error) {
			this.addResult(`❌ ${filePath} missing`, false)
		}
	}

	async readFile(filePath) {
		try {
			return await fs.readFile(path.join(this.rootDir, filePath), 'utf8')
		} catch (error) {
			this.addResult(`❌ Cannot read ${filePath}`, false)
			return ''
		}
	}

	addResult(message, success) {
		this.results.push({ message, success })
		console.log(message)
	}

	generateReport() {
		console.log('\n📊 Validation Summary:')
		console.log('=' .repeat(60))
		
		const passed = this.results.filter(r => r.success).length
		const failed = this.results.filter(r => !r.success).length
		
		console.log(`Total checks: ${this.results.length}`)
		console.log(`Passed: ${passed}`)
		console.log(`Failed: ${failed}`)
		
		if (failed > 0) {
			console.log('\n❌ Some validations failed:')
			this.results.filter(r => !r.success).forEach(r => {
				console.log(`  ${r.message}`)
			})
			console.log('\n🔧 Please fix the issues above before using AST Database functionality.')
		} else {
			console.log('\n🎉 All validations passed!')
			console.log('✨ AST Database functionality is ready to use.')
			console.log('\n📋 What was implemented:')
			console.log('  • Real workspace scanning (no more mock data)')
			console.log('  • Real-time progress monitoring via gRPC')
			console.log('  • Progress bar that starts from 0% and increments correctly')
			console.log('  • Auto-scan configuration in Features tab (not global settings)')
			console.log('  • Complete end-to-end data flow from backend to frontend')
			console.log('  • Proper error handling and null safety')
		}
		
		console.log('=' .repeat(60))
	}
}

// Run validation
if (require.main === module) {
	const validator = new AstDatabaseValidator()
	validator.validate().catch(console.error)
}

module.exports = AstDatabaseValidator
