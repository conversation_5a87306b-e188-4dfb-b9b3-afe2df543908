/**
 * End-to-end example demonstrating AST Database integration
 * This example shows how to use the AST database for enhanced autocomplete
 */

import * as vscode from "vscode"
import { AstDatabaseService } from "../AstDatabaseService"
import { ContextGatherer } from "../../autocomplete/ContextGatherer"

/**
 * Example: Setting up AST database for a workspace
 */
export async function setupAstDatabase(): Promise<void> {
	console.log("=== AST Database Setup Example ===")

	// Get the current workspace
	const workspaceFolders = vscode.workspace.workspaceFolders
	if (!workspaceFolders || workspaceFolders.length === 0) {
		console.error("No workspace folder found")
		return
	}

	const workspacePath = workspaceFolders[0].uri.fsPath
	console.log(`Setting up AST database for workspace: ${workspacePath}`)

	try {
		// Initialize AST database service
		const astService = AstDatabaseService.getInstance()
		await astService.initialize(workspacePath)

		console.log("✓ AST database service initialized")

		// Get initial status
		const initialStatus = await astService.getDatabaseStatus({ metadata: undefined })
		console.log("Initial status:", initialStatus.status?.astate)

		// Start workspace scan
		console.log("Starting workspace scan...")
		await astService.startWorkspaceScan({
			metadata: undefined,
			workspacePath,
			options: {
				maxFiles: 500,
				includeExtensions: ["ts", "js", "tsx", "jsx", "py"],
				excludePatterns: ["node_modules", ".git", "dist", "build"],
			},
		})

		console.log("✓ Workspace scan started")

		// Wait for scan to complete (in real usage, you'd use progress callbacks)
		await new Promise((resolve) => setTimeout(resolve, 5000))

		// Get final status
		const finalStatus = await astService.getDatabaseStatus({ metadata: undefined })
		console.log(`✓ Scan completed: ${finalStatus.status?.astIndexSymbolsTotal} symbols indexed`)
	} catch (error) {
		console.error("Failed to setup AST database:", error)
	}
}

/**
 * Example: Using AST database for enhanced autocomplete
 */
export async function demonstrateEnhancedAutocomplete(): Promise<void> {
	console.log("\n=== Enhanced Autocomplete Example ===")

	try {
		// Get the active text editor
		const editor = vscode.window.activeTextEditor
		if (!editor) {
			console.log("No active editor found")
			return
		}

		const document = editor.document
		const position = editor.selection.active

		console.log(`Getting context for ${document.fileName} at line ${position.line + 1}`)

		// Create context gatherer with AST database enabled
		const contextGatherer = new ContextGatherer(
			20, // maxPrecedingLines
			10, // maxFollowingLines
			20, // maxImports
			5, // maxDefinitionsToFetch
			true, // useAstDatabase
		)

		// Get context with AST database enhancement
		const context = await contextGatherer.gatherContext(
			document,
			position,
			true, // useImports
			true, // useDefinitions
		)

		console.log(`✓ Context retrieved:`)
		console.log(`  - Preceding lines: ${context.precedingLines.length}`)
		console.log(`  - Following lines: ${context.followingLines.length}`)
		console.log(`  - Imports: ${context.imports.length}`)
		console.log(`  - Definitions: ${context.definitions.length}`)

		// Show AST database specific definitions
		const astDefinitions = context.definitions.filter((def) => def.source === "ast_db")
		console.log(`  - AST DB definitions: ${astDefinitions.length}`)

		if (astDefinitions.length > 0) {
			console.log("AST Database definitions:")
			astDefinitions.forEach((def, index) => {
				console.log(`    ${index + 1}. ${def.filepath} (lines ${def.range.start.line + 1}-${def.range.end.line + 1})`)
			})
		}

		// Get performance metrics
		const metrics = contextGatherer.getAstPerformanceMetrics()
		console.log(`✓ AST DB Performance:`)
		console.log(`  - Total requests: ${metrics.totalRequests}`)
		console.log(`  - Success rate: ${((metrics.successfulRequests / metrics.totalRequests) * 100).toFixed(1)}%`)
		console.log(`  - Average response time: ${metrics.averageResponseTime.toFixed(0)}ms`)
	} catch (error) {
		console.error("Failed to demonstrate enhanced autocomplete:", error)
	}
}

/**
 * Example: Searching for symbols in the AST database
 */
export async function demonstrateSymbolSearch(): Promise<void> {
	console.log("\n=== Symbol Search Example ===")

	try {
		const astService = AstDatabaseService.getInstance()

		// Search for class definitions
		console.log("Searching for classes...")
		const classSearch = await astService.searchDefinitions({
			metadata: undefined,
			symbolName: "class",
			limit: 5,
		})

		console.log(`✓ Found ${classSearch.definitions.length} class definitions:`)
		classSearch.definitions.forEach((def, index) => {
			console.log(`  ${index + 1}. ${def.officialPath.join("::")} (${def.symbolType})`)
		})

		// Search for function definitions
		console.log("\nSearching for functions...")
		const functionSearch = await astService.searchDefinitions({
			metadata: undefined,
			symbolName: "function",
			limit: 5,
		})

		console.log(`✓ Found ${functionSearch.definitions.length} function definitions:`)
		functionSearch.definitions.forEach((def, index) => {
			console.log(`  ${index + 1}. ${def.officialPath.join("::")} (${def.symbolType})`)
		})

		// Get related symbols
		if (classSearch.definitions.length > 0) {
			const firstClass = classSearch.definitions[0]
			const symbolPath = firstClass.officialPath.join("::")

			console.log(`\nGetting related symbols for: ${symbolPath}`)
			const relatedSymbols = await astService.getRelatedSymbols({
				metadata: undefined,
				symbolPath,
				maxResults: 5,
			})

			console.log(`✓ Found ${relatedSymbols.symbols.length} related symbols:`)
			relatedSymbols.symbols.forEach((symbol, index) => {
				console.log(`  ${index + 1}. ${symbol}`)
			})
		}
	} catch (error) {
		console.error("Failed to demonstrate symbol search:", error)
	}
}

/**
 * Example: Monitoring AST database health and performance
 */
export async function demonstrateMonitoring(): Promise<void> {
	console.log("\n=== Monitoring Example ===")

	try {
		const astService = AstDatabaseService.getInstance()

		// Get performance metrics
		const metrics = astService.getPerformanceMetrics()
		console.log("Performance Metrics:")
		console.log(`  - Total requests: ${metrics.totalRequests}`)
		console.log(`  - Successful requests: ${metrics.successfulRequests}`)
		console.log(`  - Failed requests: ${metrics.failedRequests}`)
		console.log(`  - Timeouts: ${metrics.timeouts}`)
		console.log(`  - Average response time: ${metrics.averageResponseTime.toFixed(0)}ms`)
		console.log(`  - Error rate: ${(metrics.errorRate * 100).toFixed(1)}%`)

		// Get health status
		const health = astService.getHealthStatus()
		console.log(`\nHealth Status: ${health.status.toUpperCase()} ${health.isHealthy ? "✓" : "✗"}`)

		if (health.issues.length > 0) {
			console.log("Issues:")
			health.issues.forEach((issue) => console.log(`  - ${issue}`))
		} else {
			console.log("No issues detected")
		}

		// Get database statistics
		const stats = await astService.getStatistics({
			metadata: undefined,
		})

		if (stats.statistics) {
			console.log("\nDatabase Statistics:")
			console.log(`  - Total definitions: ${stats.statistics.totalDefinitions}`)
			console.log(`  - Total usages: ${stats.statistics.totalUsages}`)
			console.log(`  - Total files: ${stats.statistics.totalFiles}`)

			console.log("  - Definitions by type:")
			Object.entries(stats.statistics.definitionsByType).forEach(([type, count]) => {
				console.log(`    - ${type}: ${count}`)
			})

			if (stats.statistics.filesWithMostDefinitions.length > 0) {
				console.log("  - Files with most definitions:")
				stats.statistics.filesWithMostDefinitions.slice(0, 3).forEach((file, index) => {
					console.log(`    ${index + 1}. ${file.file}: ${file.count} definitions`)
				})
			}
		}

		// Get full status report
		console.log("\n" + astService.getStatusReport())
	} catch (error) {
		console.error("Failed to demonstrate monitoring:", error)
	}
}

/**
 * Run all examples
 */
export async function runAllExamples(): Promise<void> {
	console.log("🚀 Starting AST Database Integration Examples\n")

	try {
		await setupAstDatabase()
		await demonstrateEnhancedAutocomplete()
		await demonstrateSymbolSearch()
		await demonstrateMonitoring()

		console.log("\n✅ All examples completed successfully!")
	} catch (error) {
		console.error("\n❌ Examples failed:", error)
	}
}

// VSCode command to run examples
export function registerExampleCommands(context: vscode.ExtensionContext): void {
	const commands = [
		vscode.commands.registerCommand("cline.astdb.runExamples", runAllExamples),
		vscode.commands.registerCommand("cline.astdb.setupDatabase", setupAstDatabase),
		vscode.commands.registerCommand("cline.astdb.demonstrateAutocomplete", demonstrateEnhancedAutocomplete),
		vscode.commands.registerCommand("cline.astdb.demonstrateSearch", demonstrateSymbolSearch),
		vscode.commands.registerCommand("cline.astdb.demonstrateMonitoring", demonstrateMonitoring),
	]

	commands.forEach((command) => context.subscriptions.push(command))
}
