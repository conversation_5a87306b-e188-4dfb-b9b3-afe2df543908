/**
 * Get Scan Progress Controller
 */

import { Controller } from ".."
import type { EmptyRequest } from "@shared/proto/common"
import { ScanProgressResponse } from "@shared/proto/astdb"
import { AstDatabaseService } from "@services/astdb-service"

export async function GetScanProgress(controller: Controller, request: EmptyRequest): Promise<ScanProgressResponse> {
	try {
		// Get the AST database service instance
		const astService = AstDatabaseService.getInstance()

		// Call the service method
		const response = await astService.getScanProgress(request)

		return response
	} catch (error) {
		console.error("Failed to get scan progress:", error)

		// Return empty response if service is not available
		return ScanProgressResponse.create({ progress: undefined })
	}
}
