/**
 * AST Database Monitor
 * Provides monitoring, health checks, and performance tracking for AST database operations
 */

import * as vscode from "vscode"
import { AstDatabaseService } from "./AstDatabaseService"
import { createLogger, LogLevel } from "@services/astdb"

export interface PerformanceMetrics {
	totalRequests: number
	successfulRequests: number
	failedRequests: number
	timeouts: number
	averageResponseTime: number
	maxResponseTime: number
	minResponseTime: number
	lastRequestTime: Date | null
	uptime: number
	errorRate: number
}

export interface HealthStatus {
	isHealthy: boolean
	status: "healthy" | "degraded" | "unhealthy"
	issues: string[]
	lastCheck: Date
	databaseSize: number
	memoryUsage: number
}

export class AstDatabaseMonitor {
	private static instance: AstDatabaseMonitor | null = null
	private logger = createLogger("AstDatabaseMonitor", LogLevel.INFO)
	private metrics: PerformanceMetrics
	private healthStatus: HealthStatus
	private startTime: Date
	private healthCheckInterval: NodeJS.Timeout | null = null
	private metricsResetInterval: NodeJS.Timeout | null = null

	private constructor() {
		this.startTime = new Date()
		this.metrics = this.initializeMetrics()
		this.healthStatus = this.initializeHealthStatus()
		this.startMonitoring()
	}

	public static getInstance(): AstDatabaseMonitor {
		if (!AstDatabaseMonitor.instance) {
			AstDatabaseMonitor.instance = new AstDatabaseMonitor()
		}
		return AstDatabaseMonitor.instance
	}

	private initializeMetrics(): PerformanceMetrics {
		return {
			totalRequests: 0,
			successfulRequests: 0,
			failedRequests: 0,
			timeouts: 0,
			averageResponseTime: 0,
			maxResponseTime: 0,
			minResponseTime: Number.MAX_SAFE_INTEGER,
			lastRequestTime: null,
			uptime: 0,
			errorRate: 0,
		}
	}

	private initializeHealthStatus(): HealthStatus {
		return {
			isHealthy: true,
			status: "healthy",
			issues: [],
			lastCheck: new Date(),
			databaseSize: 0,
			memoryUsage: 0,
		}
	}

	private startMonitoring(): void {
		// Health check every 30 seconds
		this.healthCheckInterval = setInterval(() => {
			this.performHealthCheck()
		}, 30000)

		// Reset metrics every hour to prevent overflow
		this.metricsResetInterval = setInterval(() => {
			this.resetMetrics()
		}, 3600000)

		this.logger.info("AST Database monitoring started")
	}

	/**
	 * Record a request start
	 */
	public recordRequestStart(): string {
		const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
		this.metrics.totalRequests++
		this.metrics.lastRequestTime = new Date()
		return requestId
	}

	/**
	 * Record a successful request
	 */
	public recordRequestSuccess(requestId: string, responseTimeMs: number): void {
		this.metrics.successfulRequests++
		this.updateResponseTimeMetrics(responseTimeMs)
		this.updateErrorRate()

		this.logger.debug(`Request ${requestId} completed successfully in ${responseTimeMs}ms`)
	}

	/**
	 * Record a failed request
	 */
	public recordRequestFailure(requestId: string, error: Error, responseTimeMs: number): void {
		this.metrics.failedRequests++

		if (error.message.includes("timeout")) {
			this.metrics.timeouts++
		}

		this.updateResponseTimeMetrics(responseTimeMs)
		this.updateErrorRate()

		this.logger.warn(`Request ${requestId} failed after ${responseTimeMs}ms:`, error)
	}

	private updateResponseTimeMetrics(responseTime: number): void {
		// Update average response time
		const totalSuccessful = this.metrics.successfulRequests
		if (totalSuccessful === 1) {
			this.metrics.averageResponseTime = responseTime
		} else {
			this.metrics.averageResponseTime =
				(this.metrics.averageResponseTime * (totalSuccessful - 1) + responseTime) / totalSuccessful
		}

		// Update min/max response times
		this.metrics.maxResponseTime = Math.max(this.metrics.maxResponseTime, responseTime)
		this.metrics.minResponseTime = Math.min(this.metrics.minResponseTime, responseTime)
	}

	private updateErrorRate(): void {
		if (this.metrics.totalRequests > 0) {
			this.metrics.errorRate = this.metrics.failedRequests / this.metrics.totalRequests
		}
	}

	/**
	 * Perform health check
	 */
	private async performHealthCheck(): Promise<void> {
		const issues: string[] = []
		let status: "healthy" | "degraded" | "unhealthy" = "healthy"

		try {
			// Check error rate
			if (this.metrics.errorRate > 0.5) {
				issues.push(`High error rate: ${(this.metrics.errorRate * 100).toFixed(1)}%`)
				status = "unhealthy"
			} else if (this.metrics.errorRate > 0.2) {
				issues.push(`Elevated error rate: ${(this.metrics.errorRate * 100).toFixed(1)}%`)
				status = "degraded"
			}

			// Check response time
			if (this.metrics.averageResponseTime > 5000) {
				issues.push(`High average response time: ${this.metrics.averageResponseTime.toFixed(0)}ms`)
				status = status === "unhealthy" ? "unhealthy" : "degraded"
			}

			// Check timeout rate
			const timeoutRate = this.metrics.totalRequests > 0 ? this.metrics.timeouts / this.metrics.totalRequests : 0
			if (timeoutRate > 0.1) {
				issues.push(`High timeout rate: ${(timeoutRate * 100).toFixed(1)}%`)
				status = "unhealthy"
			}

			// Check database service availability
			const astService = AstDatabaseService.getInstance()
			if (astService.isCurrentlyScanning()) {
				issues.push("Database is currently scanning")
				status = status === "unhealthy" ? "unhealthy" : "degraded"
			}

			// Update memory usage (approximate)
			const memoryUsage = process.memoryUsage()
			this.healthStatus.memoryUsage = memoryUsage.heapUsed

			// Update uptime
			this.metrics.uptime = Date.now() - this.startTime.getTime()

			this.healthStatus = {
				isHealthy: status === "healthy",
				status,
				issues,
				lastCheck: new Date(),
				databaseSize: this.healthStatus.databaseSize, // Keep previous value
				memoryUsage: this.healthStatus.memoryUsage,
			}

			if (issues.length > 0) {
				this.logger.warn(`Health check found issues: ${issues.join(", ")}`)
			}
		} catch (error) {
			this.logger.error("Health check failed", error as Error)
			this.healthStatus = {
				isHealthy: false,
				status: "unhealthy",
				issues: [`Health check failed: ${error}`],
				lastCheck: new Date(),
				databaseSize: 0,
				memoryUsage: 0,
			}
		}
	}

	/**
	 * Get current performance metrics
	 */
	public getMetrics(): PerformanceMetrics {
		return { ...this.metrics }
	}

	/**
	 * Get current health status
	 */
	public getHealthStatus(): HealthStatus {
		return { ...this.healthStatus }
	}

	/**
	 * Reset metrics
	 */
	public resetMetrics(): void {
		this.logger.info("Resetting performance metrics")
		this.metrics = this.initializeMetrics()
	}

	/**
	 * Get formatted status report
	 */
	public getStatusReport(): string {
		const metrics = this.getMetrics()
		const health = this.getHealthStatus()

		const uptimeHours = (metrics.uptime / (1000 * 60 * 60)).toFixed(1)
		const successRate =
			metrics.totalRequests > 0 ? ((metrics.successfulRequests / metrics.totalRequests) * 100).toFixed(1) : "0"

		return `
AST Database Status Report
=========================
Health: ${health.status.toUpperCase()} ${health.isHealthy ? "✓" : "✗"}
Uptime: ${uptimeHours} hours
Total Requests: ${metrics.totalRequests}
Success Rate: ${successRate}%
Average Response Time: ${metrics.averageResponseTime.toFixed(0)}ms
Memory Usage: ${(health.memoryUsage / 1024 / 1024).toFixed(1)}MB

${health.issues.length > 0 ? `Issues:\n${health.issues.map((issue) => `- ${issue}`).join("\n")}` : "No issues detected"}
		`.trim()
	}

	/**
	 * Dispose of resources
	 */
	public dispose(): void {
		if (this.healthCheckInterval) {
			clearInterval(this.healthCheckInterval)
			this.healthCheckInterval = null
		}

		if (this.metricsResetInterval) {
			clearInterval(this.metricsResetInterval)
			this.metricsResetInterval = null
		}

		this.logger.info("AST Database monitoring stopped")
		AstDatabaseMonitor.instance = null
	}
}
