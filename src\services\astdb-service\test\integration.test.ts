/**
 * Integration tests for AST Database Service
 */

import * as assert from "assert"
import * as path from "path"
import * as fs from "fs/promises"
import { AstDatabaseService } from "../AstDatabaseService"
import { AstDatabaseGrpcService } from "../AstDatabaseGrpcService"
import { AstDatabaseMonitor } from "../AstDatabaseMonitor"

describe("AST Database Service Integration Tests", () => {
	let astService: AstDatabaseService
	let grpcService: AstDatabaseGrpcService
	let monitor: AstDatabaseMonitor
	let testWorkspacePath: string

	before(async () => {
		// Create a temporary test workspace
		testWorkspacePath = path.join(__dirname, "test-workspace")
		await fs.mkdir(testWorkspacePath, { recursive: true })

		// Create some test files
		await createTestFiles()

		// Initialize services
		astService = AstDatabaseService.getInstance()
		grpcService = new AstDatabaseGrpcService()
		monitor = AstDatabaseMonitor.getInstance()

		await astService.initialize(testWorkspacePath)
		await grpcService.initialize(testWorkspacePath)
	})

	after(async () => {
		// Clean up
		await astService.dispose()
		await grpcService.dispose()
		monitor.dispose()

		// Remove test workspace
		try {
			await fs.rm(testWorkspacePath, { recursive: true, force: true })
		} catch (error) {
			console.warn("Failed to clean up test workspace:", error)
		}
	})

	async function createTestFiles(): Promise<void> {
		const testFiles = [
			{
				path: "src/utils.ts",
				content: `
export class Calculator {
	add(a: number, b: number): number {
		return a + b
	}
	
	multiply(a: number, b: number): number {
		return a * b
	}
}

export function formatNumber(num: number): string {
	return num.toLocaleString()
}
				`.trim(),
			},
			{
				path: "src/main.ts",
				content: `
import { Calculator, formatNumber } from "./utils"

const calc = new Calculator()
const result = calc.add(5, 3)
console.log(formatNumber(result))
				`.trim(),
			},
			{
				path: "src/types.ts",
				content: `
export interface User {
	id: number
	name: string
	email: string
}

export type UserRole = "admin" | "user" | "guest"
				`.trim(),
			},
		]

		for (const file of testFiles) {
			const filePath = path.join(testWorkspacePath, file.path)
			await fs.mkdir(path.dirname(filePath), { recursive: true })
			await fs.writeFile(filePath, file.content)
		}
	}

	describe("Database Status", () => {
		it("should get initial database status", async () => {
			const response = await grpcService.getDatabaseStatus({ metadata: undefined })

			assert.ok(response.status)
			assert.strictEqual(response.status.astate, "ready")
			assert.strictEqual(response.status.filesTotal, 0) // No scan yet
		})
	})

	describe("Workspace Scanning", () => {
		it("should start workspace scan", async () => {
			const scanRequest = {
				metadata: undefined,
				workspacePath: testWorkspacePath,
				options: {
					maxFiles: 10,
					includeExtensions: ["ts", "js"],
					excludePatterns: ["node_modules"],
				},
			}

			const response = await grpcService.startWorkspaceScan(scanRequest)
			assert.ok(response)
		})

		it("should complete scan and update status", async function () {
			this.timeout(10000) // Allow time for scan to complete

			// Wait for scan to complete
			await new Promise((resolve) => setTimeout(resolve, 3000))

			const response = await grpcService.getDatabaseStatus({ metadata: undefined })
			assert.ok(response.status)
			assert.ok(response.status.astIndexFilesTotal > 0)
			assert.ok(response.status.astIndexSymbolsTotal > 0)
		})
	})

	describe("Definition Search", () => {
		it("should search for definitions", async () => {
			const searchRequest = {
				metadata: undefined,
				symbolName: "Calculator",
				limit: 10,
			}

			const response = await grpcService.searchDefinitions(searchRequest)
			assert.ok(response.definitions)
			assert.ok(response.definitions.length > 0)

			const calculatorDef = response.definitions.find((def) => def.officialPath.includes("Calculator"))
			assert.ok(calculatorDef)
			assert.strictEqual(calculatorDef.symbolType, "Class")
		})

		it("should search for function definitions", async () => {
			const searchRequest = {
				metadata: undefined,
				symbolName: "formatNumber",
				limit: 5,
			}

			const response = await grpcService.searchDefinitions(searchRequest)
			assert.ok(response.definitions)

			const formatNumberDef = response.definitions.find((def) => def.officialPath.includes("formatNumber"))
			assert.ok(formatNumberDef)
			assert.strictEqual(formatNumberDef.symbolType, "Function")
		})
	})

	describe("Context Retrieval", () => {
		it("should get context for autocomplete", async () => {
			const contextRequest = {
				metadata: undefined,
				filePath: path.join(testWorkspacePath, "src/main.ts"),
				line: 3,
				character: 10,
				maxTokens: 1000,
			}

			const response = await grpcService.getContext(contextRequest)
			assert.ok(response.contextFiles)
			assert.ok(response.extraContext !== undefined)
		})
	})

	describe("Related Symbols", () => {
		it("should get related symbols", async () => {
			const relatedRequest = {
				metadata: undefined,
				symbolPath: "Calculator",
				maxResults: 5,
			}

			const response = await grpcService.getRelatedSymbols(relatedRequest)
			assert.ok(response.symbols)
			assert.ok(Array.isArray(response.symbols))
		})
	})

	describe("Statistics", () => {
		it("should get database statistics", async () => {
			const statsRequest = {
				metadata: undefined,
			}

			const response = await grpcService.getStatistics(statsRequest)
			assert.ok(response.statistics)
			assert.ok(response.statistics.totalDefinitions > 0)
			assert.ok(response.statistics.totalFiles > 0)
			assert.ok(response.statistics.definitionsByType)
		})
	})

	describe("Performance Monitoring", () => {
		it("should track performance metrics", () => {
			const metrics = astService.getPerformanceMetrics()
			assert.ok(metrics)
			assert.ok(typeof metrics.totalRequests === "number")
			assert.ok(typeof metrics.successfulRequests === "number")
			assert.ok(typeof metrics.averageResponseTime === "number")
		})

		it("should provide health status", () => {
			const health = astService.getHealthStatus()
			assert.ok(health)
			assert.ok(typeof health.isHealthy === "boolean")
			assert.ok(["healthy", "degraded", "unhealthy"].includes(health.status))
			assert.ok(Array.isArray(health.issues))
		})

		it("should generate status report", () => {
			const report = astService.getStatusReport()
			assert.ok(typeof report === "string")
			assert.ok(report.includes("AST Database Status Report"))
		})
	})

	describe("Database Management", () => {
		it("should clear database", async () => {
			const clearRequest = {
				metadata: undefined,
			}

			const response = await grpcService.clearDatabase(clearRequest)
			assert.ok(response)

			// Verify database is cleared
			const statusResponse = await grpcService.getDatabaseStatus({ metadata: undefined })
			assert.strictEqual(statusResponse.status?.astIndexSymbolsTotal, 0)
		})
	})

	describe("Error Handling", () => {
		it("should handle invalid file paths gracefully", async () => {
			const contextRequest = {
				metadata: undefined,
				filePath: "/nonexistent/file.ts",
				line: 1,
				character: 1,
				maxTokens: 100,
			}

			try {
				const response = await grpcService.getContext(contextRequest)
				// Should not throw, but return empty context
				assert.ok(response.contextFiles)
				assert.strictEqual(response.contextFiles.length, 0)
			} catch (error) {
				// Error is acceptable for invalid paths
				assert.ok(error instanceof Error)
			}
		})

		it("should handle search for non-existent symbols", async () => {
			const searchRequest = {
				metadata: undefined,
				symbolName: "NonExistentSymbol",
				limit: 10,
			}

			const response = await grpcService.searchDefinitions(searchRequest)
			assert.ok(response.definitions)
			assert.strictEqual(response.definitions.length, 0)
		})
	})
})
